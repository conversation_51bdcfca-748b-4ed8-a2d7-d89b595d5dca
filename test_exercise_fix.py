#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试运动处方模型修复
"""

def test_exercise_models():
    """测试运动处方模型"""
    
    print("🧪 测试运动处方模型")
    print("="*50)
    
    try:
        from plan.exercise import ExerciseComponent, RxExercise
        print("✅ 模型导入成功")
        
        # 测试创建运动组件
        component1 = ExerciseComponent(
            type="有氧运动",
            intensity="中",
            proportion="50%",
            frequency="每周3-5次",
            duration="30-45分钟",
            notes="根据心率调整强度"
        )
        
        component2 = ExerciseComponent(
            type="抗阻运动",
            intensity="中",
            proportion="30%",
            frequency="每周2-3次",
            duration="20-30分钟",
            notes="注意动作标准"
        )
        
        print("✅ 运动组件创建成功")
        print(f"   • {component1.type}: {component1.intensity}强度")
        print(f"   • {component2.type}: {component2.intensity}强度")
        
        # 测试创建运动处方
        exercise_plan = RxExercise(
            name="高血压患者运动处方",
            goal="改善心血管健康，控制血压",
            components=[component1, component2],
            avoid=["高强度间歇训练", "举重过重"],
            overall="循序渐进，避免过度疲劳"
        )
        
        print("✅ 运动处方创建成功")
        print(f"   • 处方名称: {exercise_plan.name}")
        print(f"   • 训练目标: {exercise_plan.goal}")
        print(f"   • 训练组成: {len(exercise_plan.components)} 项")
        print(f"   • 避免运动: {len(exercise_plan.avoid)} 项")
        
        # 测试验证器 - 空的 components 列表
        print("\n🔍 测试验证器...")
        try:
            invalid_plan = RxExercise(
                name="无效处方",
                goal="测试",
                components=[],  # 空列表，应该触发验证错误
                avoid=[],
                overall="测试"
            )
            print("❌ 验证器未生效 - 应该拒绝空的 components 列表")
        except ValueError as e:
            print(f"✅ 验证器正常工作: {e}")
        
        # 测试转换为字典
        plan_dict = exercise_plan.model_dump()
        print(f"\n📄 转换为字典成功，包含键: {list(plan_dict.keys())}")
        
        # 测试转换为JSON
        plan_json = exercise_plan.model_dump_json(ensure_ascii=False, indent=2)
        print(f"📄 转换为JSON成功，长度: {len(plan_json)} 字符")
        
        print("\n🎉 所有测试通过！")
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def test_field_validation():
    """测试字段验证"""
    
    print("\n🔍 详细字段验证测试")
    print("-"*30)
    
    try:
        from plan.exercise import ExerciseComponent, RxExercise
        
        # 测试运动类型验证
        print("测试运动类型验证...")
        try:
            invalid_component = ExerciseComponent(
                type="无效运动类型",  # 应该只接受指定的字面量
                intensity="中",
                proportion="50%",
                frequency="每周3次",
                duration="30分钟"
            )
            print("❌ 运动类型验证失败")
        except ValueError:
            print("✅ 运动类型验证正常")
        
        # 测试强度验证
        print("测试强度验证...")
        try:
            invalid_component = ExerciseComponent(
                type="有氧运动",
                intensity="超高",  # 应该只接受 "低"、"中"、"高"
                proportion="50%",
                frequency="每周3次",
                duration="30分钟"
            )
            print("❌ 强度验证失败")
        except ValueError:
            print("✅ 强度验证正常")
        
        print("✅ 字段验证测试完成")
        
    except Exception as e:
        print(f"❌ 字段验证测试失败: {e}")

if __name__ == "__main__":
    test_exercise_models()
    test_field_validation()
