#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
并发调用演示 - 模拟营养和运动方案生成
"""

import asyncio
import time
import random
from concurrent.futures import ThreadPoolExecutor
from model import PatientInfo

def mock_generate_nutrition(patient):
    """模拟营养方案生成"""
    # 模拟耗时操作
    time.sleep(random.uniform(1, 3))
    return {
        "type": "nutrition",
        "patient_id": patient.id,
        "intake": 1800,
        "status": "success"
    }

def mock_generate_exercise(patient):
    """模拟运动方案生成"""
    # 模拟耗时操作
    time.sleep(random.uniform(1, 3))
    return {
        "type": "exercise", 
        "patient_id": patient.id,
        "duration": "30min",
        "status": "success"
    }

async def generate_plan_concurrent(patient):
    """
    并发生成单个患者的营养和运动方案
    """
    print(f"🔄 开始处理患者 {patient.id}...")
    start_time = time.time()
    
    loop = asyncio.get_event_loop()
    
    # 使用线程池并发执行
    with ThreadPoolExecutor(max_workers=2) as executor:
        # 提交任务到线程池
        nutrition_future = loop.run_in_executor(
            executor, mock_generate_nutrition, patient
        )
        exercise_future = loop.run_in_executor(
            executor, mock_generate_exercise, patient
        )
        
        # 等待两个任务完成
        nutrition_result, exercise_result = await asyncio.gather(
            nutrition_future,
            exercise_future,
            return_exceptions=True
        )
        
        end_time = time.time()
        
        plan = {
            "patient_id": patient.id,
            "nutrition": nutrition_result,
            "exercise": exercise_result,
            "duration": end_time - start_time
        }
        
        print(f"✅ 患者 {patient.id} 处理完成 (耗时: {plan['duration']:.2f}秒)")
        return plan

def generate_plan_serial(patient):
    """
    串行生成单个患者的营养和运动方案
    """
    print(f"🔄 开始串行处理患者 {patient.id}...")
    start_time = time.time()
    
    # 串行执行
    nutrition_result = mock_generate_nutrition(patient)
    exercise_result = mock_generate_exercise(patient)
    
    end_time = time.time()
    
    plan = {
        "patient_id": patient.id,
        "nutrition": nutrition_result,
        "exercise": exercise_result,
        "duration": end_time - start_time
    }
    
    print(f"✅ 患者 {patient.id} 串行处理完成 (耗时: {plan['duration']:.2f}秒)")
    return plan

async def demo_performance_comparison():
    """演示性能对比"""
    
    # 创建测试患者
    patients = [
        PatientInfo(id="001", gender=0, age=32, height=1.65, weight=60.0, pla=1.4, tags=["高血压"]),
        PatientInfo(id="002", gender=1, age=45, height=1.75, weight=80.0, pla=1.6, tags=["糖尿病"]),
        PatientInfo(id="003", gender=0, age=28, height=1.60, weight=55.0, pla=1.8, tags=["健康"]),
    ]
    
    print("🏥 健康管理方案生成性能对比")
    print("="*60)
    
    # 1. 串行处理演示
    print("\n📝 方式1: 串行处理")
    print("-" * 30)
    serial_start = time.time()
    
    serial_results = []
    for patient in patients:
        result = generate_plan_serial(patient)
        serial_results.append(result)
    
    serial_total = time.time() - serial_start
    print(f"📊 串行处理总耗时: {serial_total:.2f} 秒")
    
    # 2. 并发处理演示
    print("\n⚡ 方式2: 并发处理 (每个患者的营养和运动方案并发)")
    print("-" * 30)
    concurrent_start = time.time()
    
    concurrent_results = []
    for patient in patients:
        result = await generate_plan_concurrent(patient)
        concurrent_results.append(result)
    
    concurrent_total = time.time() - concurrent_start
    print(f"📊 并发处理总耗时: {concurrent_total:.2f} 秒")
    
    # 3. 全部并发处理演示
    print("\n🚀 方式3: 全部并发处理 (所有患者同时处理)")
    print("-" * 30)
    full_concurrent_start = time.time()
    
    tasks = [generate_plan_concurrent(patient) for patient in patients]
    full_concurrent_results = await asyncio.gather(*tasks)
    
    full_concurrent_total = time.time() - full_concurrent_start
    print(f"📊 全部并发处理总耗时: {full_concurrent_total:.2f} 秒")
    
    # 性能对比总结
    print("\n📈 性能对比总结")
    print("="*60)
    print(f"串行处理:     {serial_total:.2f} 秒")
    print(f"单患者并发:   {concurrent_total:.2f} 秒 (提升 {serial_total/concurrent_total:.1f}x)")
    print(f"全部并发:     {full_concurrent_total:.2f} 秒 (提升 {serial_total/full_concurrent_total:.1f}x)")
    
    print(f"\n💡 性能提升:")
    print(f"   单患者并发相比串行: 约 {((serial_total - concurrent_total) / serial_total * 100):.1f}% 时间节省")
    print(f"   全部并发相比串行:   约 {((serial_total - full_concurrent_total) / serial_total * 100):.1f}% 时间节省")

async def demo_with_semaphore():
    """演示使用信号量限制并发数量"""
    
    patients = [
        PatientInfo(id=f"{i:03d}", gender=i%2, age=30+i*5, height=1.6+i*0.05, weight=60+i*5, pla=1.4, tags=["测试"])
        for i in range(1, 8)  # 创建7个患者
    ]
    
    print("\n🎛️  信号量限制并发演示")
    print("="*60)
    print(f"患者数量: {len(patients)}")
    
    # 限制最大并发数为3
    semaphore = asyncio.Semaphore(3)
    
    async def generate_with_limit(patient):
        async with semaphore:
            print(f"🔄 开始处理患者 {patient.id} (当前并发槽位已占用)")
            result = await generate_plan_concurrent(patient)
            print(f"✅ 患者 {patient.id} 完成，释放并发槽位")
            return result
    
    start_time = time.time()
    
    # 创建所有任务
    tasks = [generate_with_limit(patient) for patient in patients]
    
    # 执行所有任务
    results = await asyncio.gather(*tasks)
    
    total_time = time.time() - start_time
    
    print(f"\n📊 限制并发(3个)总耗时: {total_time:.2f} 秒")
    print(f"📊 平均每个患者: {total_time/len(patients):.2f} 秒")

if __name__ == "__main__":
    print("🧪 并发调用演示程序")
    print("="*60)
    
    # 运行性能对比演示
    asyncio.run(demo_performance_comparison())
    
    # 运行信号量限制演示
    asyncio.run(demo_with_semaphore())
    
    print("\n🎉 演示完成！")
    print("\n💡 总结:")
    print("1. 并发处理可以显著提高性能")
    print("2. 单患者并发: 营养和运动方案同时生成")
    print("3. 全部并发: 所有患者同时处理")
    print("4. 信号量限制: 控制同时处理的数量，避免资源过载")
    print("5. 根据实际情况选择合适的并发策略")
