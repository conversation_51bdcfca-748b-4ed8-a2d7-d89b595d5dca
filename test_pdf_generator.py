#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试PDF生成器
"""

from model import PatientInfo
from routers.plan.nutrition import NutritionPlan, MealStructureAdvice, MealStructureAdviceFood
from routers.plan.calc import Nutrient
from utils.pdf_generator import generate_health_plan_pdf

def create_sample_nutrition_plan() -> NutritionPlan:
    """创建示例营养方案"""
    
    # 营养素建议
    nutrients = [
        Nutrient(name="蛋白质", amount="60-80", ratio="15%-20%"),
        Nutrient(name="脂肪", amount="40-60", ratio="20%-30%"),
        Nutrient(name="碳水化合物", amount="180-230", ratio="50%-65%")
    ]
    
    # 膳食结构建议
    breakfast = MealStructureAdvice(
        name="早餐",
        items=[
            MealStructureAdviceFood(type="全谷类", amount=50, source="燕麦片"),
            MealStructureAdviceFood(type="乳制品", amount=200, source="低脂牛奶"),
            MealStructureAdviceFood(type="水果", amount=150, source="苹果")
        ]
    )
    
    lunch = MealStructureAdvice(
        name="午餐",
        items=[
            MealStructureAdviceFood(type="全谷类", amount=80, source="糙米饭"),
            MealStructureAdviceFood(type="水产品", amount=100, source="三文鱼"),
            MealStructureAdviceFood(type="蔬菜", amount=200, source="西兰花、胡萝卜")
        ]
    )
    
    dinner = MealStructureAdvice(
        name="晚餐",
        items=[
            MealStructureAdviceFood(type="谷类", amount=60, source="全麦面条"),
            MealStructureAdviceFood(type="畜禽肉类", amount=80, source="鸡胸肉"),
            MealStructureAdviceFood(type="蔬菜", amount=250, source="菠菜、番茄")
        ]
    )
    
    # 创建营养方案
    nutrition_plan = NutritionPlan(
        overview="""采用DASH饮食结合地中海饮食模式，短期目标为改善血脂和血糖水平，中期目标为减重5%-10%并改善胰岛素敏感性，长期目标为降低心血管疾病风险。建议多采用蒸、煮、炖、烤等烹调方式，减少油炸、烧烤。推荐多摄入全谷物、深海鱼类、坚果、橄榄油、低脂乳制品，限制红肉、含糖饮料、加工食品及高盐食物。""",
        intake=1800,
        meal=nutrients,
        structure=[breakfast, lunch, dinner],
        note="每日钠摄入<2000mg，每日饮水1500-2000ml，避免饮酒。建议定期监测血糖、血压和血脂水平。"
    )
    
    return nutrition_plan

def test_pdf_generation():
    """测试PDF生成功能"""
    
    # 创建测试患者
    patient = PatientInfo(
        gender=0,  # 女性
        age=32,
        height=1.65,
        weight=60.0,
        pla=1.4,
        tags=["高血压", "2型糖尿病", "高血脂"]
    )
    
    # 创建示例营养方案
    nutrition_plan = create_sample_nutrition_plan()
    
    print("患者信息：")
    print(patient.to_natural_language())
    print()
    
    print("正在生成PDF...")
    try:
        pdf_path = generate_health_plan_pdf(
            patient=patient,
            nutrition_plan=nutrition_plan,
            filename="健康管理方案_示例.pdf"
        )
        print(f"✅ PDF生成成功：{pdf_path}")
        print("📄 PDF包含以下内容：")
        print("   • 患者基本信息表")
        print("   • 膳食原则及目标")
        print("   • 每日能量需求")
        print("   • 营养素建议表")
        print("   • 一日三餐膳食结构建议")
        print("   • 注意事项和免责声明")
        
    except Exception as e:
        print(f"❌ PDF生成失败：{e}")
        print("💡 可能需要安装 reportlab 库：pip install reportlab")

if __name__ == "__main__":
    test_pdf_generation()
