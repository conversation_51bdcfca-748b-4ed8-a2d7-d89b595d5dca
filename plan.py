import json
import re
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
import asyncio

from langchain_core.output_parsers import JsonOutputParser
from langchain_core.prompts import PromptTemplate
from langchain_core.runnables import RunnablePassthrough

from utils import llm
from utils.pdf_generator import PDFGenerator

from model import PatientInfo

template = """
您是一名专业的运动与营养学专家，请根据患者的慢病类型和具体情况，为其制定个性化的随访管理方案，重点包括营养干预建议。
请按照以下结构生成营养方案内容：

1.**膳食原则及目标：** 采用以特殊目标维度为主的平衡膳食模式（如地中海饮食、DASH饮食等）。
    - 短期目标： 改善某项具体指标或症状（如改善血糖、血脂、血压等）；
    - 中期目标： 达到并维持某一健康状态（如减重、改善胰岛素敏感性等）；
    - 长期目标： 降低某种疾病风险或延缓疾病进展（如降低心血管事件风险、延缓糖尿病并发症等）；
    - 建议烹调方式： 推荐适合该疾病的合理烹饪方法（如蒸、煮、炖、烤等，避免油炸、烧烤）；
    - 食物选择建议： 明确适宜摄入的食物类别及应限制或避免的食物类别。
    示例参考：
        坚持地中海饮食为主的平衡膳食模式，短期目标为改善血脂水平，中期目标为达到并维持健康体重，长期目标为降低心血管疾病的发生风险。建议进餐应定时定量，多采用蒸、煮等方式烹调，减少油炸、烧烤；推荐多摄入全谷物、深海鱼类、坚果、橄榄油，适量控制红肉摄入。

2.**运动原则及目标：** 每周进行至少150-300分钟的中等强度有氧运动；每周间断进行2-3天的阻力运动；每周进行至少2-3天的柔韧性运动；每周至少进行2-3天的平衡运动。
    - 短期目标： 改善某项具体指标或症状（如改善血糖、血脂、血压等）；
    - 中期目标： 达到并维持某一健康状态（如减重、改善胰岛素敏感性等）；
    - 长期目标： 降低某种疾病风险或延缓疾病进展（如降低心血管事件风险、延缓糖尿病并发症等）；
    示例参考：
        建议每周进行至少150-300分钟的中等强度有氧运动；每周间断进行2-3天的阻力运动；每周进行至少2-3天的柔韧性运动；每周至少进行2-3天的平衡运动。运动锻炼的短期目标为改善血脂、降低血糖，中期目标为达到并维持健康体重，长期目标为降低CVD的发生风险。

请根据上述格式，为患者生成科学、个性化、可执行的慢病随访营养方案。
以下是患者情况:

```
{query}
```
{format_instructions}
"""

class MealNutrientAdvice(BaseModel):
    nutrient: str = Field(..., description="营养素名称，如蛋白质、脂肪等")
    amount: int = Field(..., description="推荐摄入量，单位为克（g）")
    ratio: float = Field(..., description="占每日总能量的比例，单位为百分比")

class MealStructureAdviceFood(BaseModel):
    type: str = Field(..., description="食物总类", enum=["畜禽肉类", "大豆和坚果", "蛋类", "低血糖生成指数食物", "谷类", "烹调用盐", "烹调用油", "全谷类", "乳制品", "蔬菜", "薯类", "水产品", "水果"])
    amount: int = Field(..., description="推荐量，单位为克（g）")
    source: str = Field(..., description="食物来源")

class MealStructureAdvice(BaseModel):
    name: str = Field(..., description="餐次名称", enum=["早餐", "上午加餐", "午餐", "下午加餐", "晚餐"])
    items: List[MealStructureAdviceFood] = Field(..., description="该餐次的食物建议列表")

class NutritionPlan(BaseModel):
    overview: str = Field(..., description="膳食原则及目标")
    intake: int = Field(..., description="每日能量摄入，单位千卡")
    meal: List[MealNutrientAdvice] = Field(..., description="一日膳食能量需要量")
    structure: List[MealStructureAdvice] = Field(..., description="一日三餐膳食结构建议")
    note: Optional[str] = Field(None, description="其他注意事项")

class Exercise(BaseModel):
    type: str = Field(..., description="运动类型", enum=["有氧运动", "抗阻运动", "柔韧性训练", "平衡训练"])
    recommend: str = Field(..., description="运动方式，如：散步、慢跑、舞蹈等")
    intensity: str = Field(..., description="运动强度", enum=["低", "中", "高"])
    proportion: str = Field(..., description="运动类型占比")
    frequency: str = Field(..., description="运动频率")
    duration: str = Field(..., description="运动时长")
    progression: str = Field(..., description="运动进阶，如：进展速度取决于基线健康状况、年龄、体重、当前健康状况和个人目标;建议强 度和频率逐渐递增。")
    guidance: str = Field(..., description="运动指导")
    notes: str = Field("暂无", description="注意事项")

class ExercisePlan(BaseModel):
    overview: str = Field(..., description="运动原则及目标")
    components: List[Exercise] = Field(..., min_items=1, description="训练组成")
    avoid: List[str] = Field(default=[], description="避免运动")
    overall: str = Field(..., description="整体注意事项")


class Output(BaseModel):
    nutrition: NutritionPlan = Field(..., description="营养方案")
    exercise: ExercisePlan = Field(..., description="运动方案")

parser = JsonOutputParser(pydantic_object=Output)
prompt = PromptTemplate(
    template=template,
    input_variables=["query"],
    partial_variables={"format_instructions": parser.get_format_instructions()}
)

def remove_think_tags(text: str) -> str:
    """
    移除文本中的<think></think>标签及其内容
    
    Args:
        text: 包含think标签的文本
        
    Returns:
        str: 移除think标签后的文本
    """
    if not isinstance(text, str):
        text = str(text)
    # 使用正则表达式匹配并移除<think></think>标签及其内容
    return re.sub(r'<think>.*?</think>', '', text, flags=re.DOTALL).strip()

def generate(query: str):
    chain = (
        {"query": RunnablePassthrough()}
        | prompt
        | llm.local
        | (lambda x: x.content if hasattr(x, 'content') else str(x))  # 确保获取LLM输出的文本内容
        | remove_think_tags
        | parser
    )
    output_dict = chain.invoke(query)
    print(json.dumps(output_dict, ensure_ascii=False, indent=2))
    return output_dict

async def generate_plan(patient: PatientInfo) -> Dict[str, Any]:
    """
    并发生成营养方案和运动方案

    Args:
        patient: 患者信息

    Returns:
        包含营养方案和运动方案的字典
    """
    # 并发执行营养方案和运动方案的生成
    nutrition_task = asyncio.create_task(nutrition.generate_with_llm(patient))
    exercise_task = asyncio.create_task(exercise.generate_with_llm(patient))

    # 等待所有任务完成
    nutrition_plan, exercise_plan = await asyncio.gather(nutrition_task, exercise_task)

    # 生成PDF
    pdf_generator = PDFGenerator(f"output/health_plan_{patient.id}.pdf")
    pdf_generator.generate_health_plan(
        patient_info=patient.dict(),
        nutrition_plan=nutrition_plan.dict(),
        exercise_plan=exercise_plan.dict()
    )

    return {
        "nutrition": nutrition_plan,
        "exercise": exercise_plan
    }

if __name__ == "__main__":
    generate("32岁女性，患有高血压，血脂偏高，血糖偏高，体重偏重。")