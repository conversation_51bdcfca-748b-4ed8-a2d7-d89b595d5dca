#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
演示PDF生成功能 - 使用模拟数据，不依赖LLM服务
"""

from datetime import datetime
from model import PatientInfo
from routers.plan.nutrition import NutritionPlan, MealStructureAdvice, MealStructureAdviceFood
from routers.plan.calc import Nutrient

def create_mock_nutrition_plan(patient: PatientInfo) -> NutritionPlan:
    """
    创建模拟营养方案（不使用LLM）
    
    Args:
        patient: 患者信息
        
    Returns:
        NutritionPlan: 模拟的营养方案
    """
    
    # 根据患者信息调整能量需求
    base_energy = 1600 if patient.gender == 0 else 1800  # 女性/男性基础能量
    energy_adjustment = (patient.age - 30) * -5  # 年龄调整
    activity_adjustment = (patient.pla - 1.4) * 200  # 活动水平调整
    total_energy = int(base_energy + energy_adjustment + activity_adjustment)
    
    # 营养素建议
    protein_ratio = "15%-20%" if patient.age < 65 else "18%-22%"
    fat_ratio = "20%-30%"
    carb_ratio = "50%-65%"
    
    nutrients = [
        Nutrient(
            name="蛋白质", 
            amount=f"{int(total_energy * 0.15 / 4)}-{int(total_energy * 0.20 / 4)}", 
            ratio=protein_ratio
        ),
        Nutrient(
            name="脂肪", 
            amount=f"{int(total_energy * 0.20 / 9)}-{int(total_energy * 0.30 / 9)}", 
            ratio=fat_ratio
        ),
        Nutrient(
            name="碳水化合物", 
            amount=f"{int(total_energy * 0.50 / 4)}-{int(total_energy * 0.65 / 4)}", 
            ratio=carb_ratio
        )
    ]
    
    # 根据疾病标签调整膳食建议
    disease_considerations = []
    cooking_methods = "蒸、煮、炖、烤"
    avoid_foods = []
    
    if "高血压" in patient.tags:
        disease_considerations.append("控制钠盐摄入(<5g/天)")
        avoid_foods.extend(["高盐食品", "腌制食品"])
    
    if "糖尿病" in patient.tags or "2型糖尿病" in patient.tags:
        disease_considerations.append("控制血糖波动")
        avoid_foods.extend(["高糖食品", "精制糖"])
    
    if "高血脂" in patient.tags:
        disease_considerations.append("控制饱和脂肪摄入")
        avoid_foods.extend(["高脂肪食品", "油炸食品"])
    
    # 膳食原则
    overview = f"""采用DASH饮食结合地中海饮食模式，针对{patient.to_natural_language()}的具体情况制定。
短期目标：改善血糖、血脂、血压等指标；
中期目标：达到并维持健康体重，改善胰岛素敏感性；
长期目标：降低心血管疾病风险，延缓慢性病进展。
建议烹调方式：多采用{cooking_methods}等方式，避免油炸、烧烤。
食物选择：推荐全谷物、深海鱼类、坚果、橄榄油、低脂乳制品，限制{', '.join(avoid_foods)}。
{' '.join(disease_considerations)}。"""
    
    # 膳食结构建议
    breakfast = MealStructureAdvice(
        name="早餐",
        items=[
            MealStructureAdviceFood(type="全谷类", amount=50, source="燕麦片或全麦面包"),
            MealStructureAdviceFood(type="乳制品", amount=200, source="低脂牛奶或无糖酸奶"),
            MealStructureAdviceFood(type="水果", amount=150, source="苹果、香蕉或橙子"),
            MealStructureAdviceFood(type="蛋类", amount=50, source="水煮蛋")
        ]
    )
    
    lunch = MealStructureAdvice(
        name="午餐",
        items=[
            MealStructureAdviceFood(type="全谷类", amount=80, source="糙米饭或全麦面条"),
            MealStructureAdviceFood(type="水产品", amount=100, source="三文鱼、鲈鱼或虾"),
            MealStructureAdviceFood(type="蔬菜", amount=200, source="西兰花、胡萝卜、青菜"),
            MealStructureAdviceFood(type="烹调用油", amount=10, source="橄榄油或菜籽油")
        ]
    )
    
    dinner = MealStructureAdvice(
        name="晚餐",
        items=[
            MealStructureAdviceFood(type="谷类", amount=60, source="小米粥或燕麦粥"),
            MealStructureAdviceFood(type="畜禽肉类", amount=80, source="去皮鸡胸肉或瘦牛肉"),
            MealStructureAdviceFood(type="蔬菜", amount=250, source="菠菜、番茄、黄瓜"),
            MealStructureAdviceFood(type="大豆和坚果", amount=30, source="核桃或杏仁")
        ]
    )
    
    # 注意事项
    notes = []
    notes.append(f"每日饮水1500-2000ml")
    notes.append("建议少食多餐，定时定量")
    
    if "高血压" in patient.tags:
        notes.append("每日钠摄入<2000mg")
    if "糖尿病" in patient.tags or "2型糖尿病" in patient.tags:
        notes.append("餐后适当运动，监测血糖")
    if "高血脂" in patient.tags:
        notes.append("定期监测血脂水平")
    
    notes.append("避免饮酒和吸烟")
    
    # 创建营养方案
    nutrition_plan = NutritionPlan(
        overview=overview,
        intake=total_energy,
        meal=nutrients,
        structure=[breakfast, lunch, dinner],
        note="；".join(notes) + "。"
    )
    
    return nutrition_plan

def demo_pdf_generation():
    """演示PDF生成功能"""
    
    print("🏥 健康管理方案PDF生成演示")
    print("="*50)
    
    # 创建测试患者
    patient = PatientInfo(
        gender=0,  # 女性
        age=32,
        height=1.65,
        weight=60.0,
        pla=1.4,
        tags=["高血压", "2型糖尿病", "高血脂"]
    )
    
    print("👤 患者信息：")
    print(patient.to_natural_language())
    print()
    
    print("🔄 正在生成营养方案...")
    
    # 创建模拟营养方案
    nutrition_plan = create_mock_nutrition_plan(patient)
    
    print("✅ 营养方案生成完成！")
    print(f"📊 每日能量需求：{nutrition_plan.intake} 千卡")
    print(f"🥗 营养素建议：{len(nutrition_plan.meal)} 项")
    print(f"🍽️  膳食结构：{len(nutrition_plan.structure)} 餐")
    print()
    
    print("📄 正在生成PDF文档...")
    
    try:
        from utils.pdf_generator import generate_health_plan_pdf
        
        # 生成PDF文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        pdf_filename = f"健康管理方案_{timestamp}.pdf"
        
        # 生成PDF
        pdf_path = generate_health_plan_pdf(
            patient=patient,
            nutrition_plan=nutrition_plan,
            filename=pdf_filename
        )
        
        print(f"✅ PDF生成成功！")
        print(f"📁 文件路径：{pdf_path}")
        print()
        print("📋 PDF包含内容：")
        print("   • 患者基本信息表")
        print("   • 膳食原则及目标")
        print("   • 每日能量需求")
        print("   • 营养素建议表格")
        print("   • 一日三餐膳食结构")
        print("   • 注意事项和免责声明")
        
    except ImportError:
        print("❌ PDF生成失败：请先安装 reportlab 库")
        print("💡 安装命令：pip install reportlab")
    except Exception as e:
        print(f"❌ PDF生成失败：{e}")
    
    print("\n🎉 演示完成！")

if __name__ == "__main__":
    demo_pdf_generation()
