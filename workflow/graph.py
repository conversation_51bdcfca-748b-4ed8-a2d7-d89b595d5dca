from langgraph.constants import START, END
from langgraph.graph import StateGraph

from utils.llm import init_langsmith
from utils.pdf_generator import PDFGenerator
from workflow.nodes.nutrition import nutrition_plan
from workflow.nodes.exercise import exercise_plan
from workflow.nodes.summary import summary
from workflow.model import State, PatientInfo

builder = StateGraph(State)
builder.add_node("nutrition_node", nutrition_plan)
builder.add_node("exercise_node", exercise_plan)
builder.add_node("summary_node", summary)
builder.add_edge(START, "nutrition_node")
builder.add_edge(START, "exercise_node")
builder.add_edge("nutrition_node", END)
builder.add_edge("exercise_node", END)
graph = builder.compile()

if __name__ == "__main__":
    init_langsmith("iFollow")
    p = graph.invoke({
        "patient": PatientInfo(gender="M", age=68, height=1.71, weight=81.0, pla=1.4, tags=["高脂血症低危", "糖尿病"])
    })
    print("---------------------> ", p)
    # 生成PDF
    pdf_generator = PDFGenerator(f"output/健康管理方案_001.pdf")
    pdf_generator.generate_health_plan(
        patient_info=p["patient"].model_dump(),
        nutrition_plan=p["nutrition"],
        exercise_plan=p["exercise"]
    )

