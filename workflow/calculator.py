from typing import List

from pydantic import BaseModel, Field

from workflow.model import PatientInfo, EnergyNutrient, Nutrient


def calc_all(patient: PatientInfo) -> EnergyNutrient:
    """
        根据患者具体情况，计算一日膳食能量需要量和营养素建议。

        Args:
            patient: 患者信息

        Returns:
            dict: 包含能量需求和营养素建议的字典
    """
    # 计算每日能量需求量
    eer = calc_energy(patient)

    # 计算营养素建议
    nutrients = calc_nutrients(eer, patient.age)

    # 返回计算结果字典
    return EnergyNutrient(eer=eer, nutrients=nutrients)

def calc_energy(patient: PatientInfo):
    """ 计算一日膳食能量需要量 """
    age = patient.age
    gender = patient.gender
    gender_code = 0 if gender == "M" else 1
    weight = patient.weight
    height = patient.height
    pla = patient.pla

    if patient.age < 3:
        if patient.gender == "M":
            eer = (0.118 * weight + 3.59 * height - 1.55) * 238.85 * pla * 1.01
        else:
            eer = (0.127 * weight + 2.94 * height - 1.2) * 238.85 * pla * 1.01
    elif 3 <= age < 10:
        if gender == "M":
            eer = (0.0632 * weight + 1.31 * height + 1.28) * 238.85 * pla * 1.01
        else:
            eer = (0.0666 * weight + 0.878 * height + 1.46) * 238.85 * pla * 1.01
    elif 10 <= age < 18:
        if gender == "M":
            eer = (0.0651 * weight + 1.11 * height + 1.25) * 238.85 * pla * 1.01
        else:
            eer = (0.0393 * weight + 1.04 * height + 1.93) * 238.85 * pla * 1.01
    elif 18 <= age < 50:
        eer = (14.52 * weight - 155.88 * gender_code + 565.79) * pla
    elif 50 <= age < 65:
        eer = (14.52 * weight - 155.88 * gender_code + 565.79) * pla * 0.95
    elif 65 <= age < 75:
        eer = (14.52 * weight - 155.88 * gender_code + 565.79) * pla * 0.925
    else:
        eer = (14.52 * weight - 155.88 * gender_code + 565.79) * pla * 0.9
    return round(eer)

def calc_nutrients(energy: int, age: int) -> List[Nutrient]:
    """
    计算一日三餐营养素建议

    Args:
        energy: 每日能量需求量（千卡）
        age: 年龄

    Returns:
        List[Nutrient]: 营养素建议列表
    """
    return [
        calc_nutrients_carb(energy, age),
        calc_nutrients_fat(energy, age),
        calc_nutrients_protein(energy, age)
    ]

def calc_nutrients_carb(eer: int,age: int):
    name = "碳水化合物"
    if age < 1:
        return Nutrient(name=name, amount="80", ratio="50%-65%")
    else:
        return Nutrient(name=name, amount=f"{round(eer * 0.5 / 4)}-{round(eer * 0.65 / 4)}", ratio="50%-65%")

def calc_nutrients_fat(eer: int, age: int):
    name = "脂肪"
    if age < 1:
        return Nutrient(name=name, amount=f"{round(eer * 0.4 / 9)}", ratio="40%")
    elif 1 <= age < 4:
        return Nutrient(name=name, amount=f"{round(eer * 0.35 / 9)}", ratio="35%")
    else:
        return Nutrient(name=name, amount=f"{round(eer * 0.2 / 9)}-{round(eer * 0.3 / 9)}", ratio="20%-30%")

def calc_nutrients_protein(eer: int, age: int):
    name = "蛋白质"
    if age < 65:
        return Nutrient(name=name, amount=f"{round(eer * 0.1 / 4)}-{round(eer * 0.2 / 4)}", ratio="10%-20%")
    else:
        return Nutrient(name=name, amount=f"{round(eer * 0.15 / 4)}-{round(eer * 0.2 / 4)}", ratio="15%-20%")
