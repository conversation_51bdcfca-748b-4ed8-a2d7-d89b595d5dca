from typing import List, Optional, Literal
from pydantic import BaseModel, Field

from langchain_core.output_parsers import JsonOutputParser
from langchain_core.prompts import PromptTemplate

from langgraph.prebuilt import create_react_agent

from plan import nutrition_calculator
from plan.nutrition_calculator import EnergyNutrient
from utils import llm

template = """
请根据患者具体情况，为其制定个性化的营养方案。
请按照以下结构生成营养方案内容：

1.**膳食原则及目标：** 采用以特殊目标维度为主的平衡膳食模式（如地中海饮食、DASH饮食等）。
    - 短期目标： 改善某项具体指标或症状（如改善血糖、血脂、血压等）；
    - 中期目标： 达到并维持某一健康状态（如减重、改善胰岛素敏感性等）；
    - 长期目标： 降低某种疾病风险或延缓疾病进展（如降低心血管事件风险、延缓糖尿病并发症等）；
    - 建议烹调方式： 推荐适合该疾病的合理烹饪方法（如蒸、煮、炖、烤等，避免油炸、烧烤）；
    - 食物选择建议： 明确适宜摄入的食物类别及应限制或避免的食物类别。
    示例参考：
        坚持地中海饮食为主的平衡膳食模式，短期目标为改善血脂水平，中期目标为达到并维持健康体重，长期目标为降低心血管疾病的发生风险。建议进餐应定时定量，多采用蒸、煮等方式烹调，减少油炸、烧烤；推荐多摄入全谷物、深海鱼类、坚果、橄榄油，适量控制红肉摄入。

2.**一日膳食能量需要量：** 根据患者具体情况，计算一日膳食能量需要量和营养素建议。

3.**一日三餐膳食结构建议：** 根据患者具体情况，制定一日三餐膳食结构建议。

4.**其他注意事项：** 根据患者具体情况，制定其他注意事项。

请根据上述格式，为患者生成科学、个性化、可执行的慢病随访营养方案。

以下是患者情况:

```
{query}
```
{format_instructions}
"""

class MealNutrientAdvice(BaseModel):
    nutrient: str = Field(..., description="营养素名称，如蛋白质、脂肪等")
    amount: int = Field(..., description="推荐摄入量，单位为克（g）")
    ratio: float = Field(..., description="占每日总能量的比例，单位为百分比")

class MealStructureAdviceFood(BaseModel):
    type: Literal["畜禽肉类", "大豆和坚果", "蛋类", "低血糖生成指数食物", "谷类", "烹调用盐", "烹调用油", "全谷类", "乳制品", "蔬菜", "薯类", "水产品", "水果"] = Field(..., description="食物总类")
    amount: int = Field(..., description="推荐量，单位为克（g）")
    source: str = Field(..., description="食物来源")

class MealStructureAdvice(BaseModel):
    name: Literal["早餐", "上午加餐", "午餐", "下午加餐", "晚餐"] = Field(..., description="餐次名称")
    items: List[MealStructureAdviceFood] = Field(..., description="该餐次的食物建议列表")

class NutritionPlan(BaseModel):
    overview: str = Field(..., description="膳食的原则及目标")
    intake: int = Field(..., description="一日膳食能量需要量，单位千卡")
    meal: List[MealNutrientAdvice] = Field(..., description="一日三餐营养素建议")
    structure: List[MealStructureAdvice] = Field(..., description="一日三餐膳食结构建议")
    note: Optional[str] = Field(None, description="其他注意事项")

# @tool("calc_nutrient", parse_docstring=True)
def calc_nutrients(gender: int, age: int, height: float, weight: float, pla: float = 1.6) -> EnergyNutrient:
    """
        根据患者具体情况，计算一日膳食能量需要量和营养素建议。

        Args:
            gender: 性别字段，性别字段，必须是 1（男）或 0（女）
            age: 年龄
            height: 身高，单位：米
            weight: 体重，单位：公斤
            pla: 体力活动水平，默认值为 1.6
    """
    return calc.calc(gender, age, height, weight, pla)


parser = JsonOutputParser(pydantic_object=NutritionPlan)
format_instructions = parser.get_format_instructions()

prompt = PromptTemplate(
    template=template,
    input_variables=["query"],
    partial_variables={"format_instructions": format_instructions}
)
agent = create_react_agent(
    model=llm.qwen3_30b_a3b,
    prompt="您是一名专业的营养专家",
    tools=[calc_nutrients],
)

# def nutrition_agent(state: State) -> State:
    

if __name__ == "__main__":
    llm.init_langsmith("健康管理方案")
    user_input = "32岁女性，身高160cm, 体重49.5公斤，患有高血压，血脂偏高，血糖偏高，体重偏重。"
    formatted_prompt = prompt.format(query=user_input)
    print(agent.invoke({"messages": {"role": "user", "content": formatted_prompt}}))