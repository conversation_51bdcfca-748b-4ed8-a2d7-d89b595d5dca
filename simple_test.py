#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单测试 - 验证模块导入和基本功能
"""

def test_imports():
    """测试模块导入"""
    try:
        from model import PatientInfo
        print("✅ PatientInfo 导入成功")
        
        # 创建测试患者
        patient = PatientInfo(
            gender=0,
            age=32,
            height=1.65,
            weight=60.0,
            pla=1.4,
            tags=["高血压", "糖尿病"]
        )
        
        print("✅ PatientInfo 创建成功")
        print(f"患者信息: {patient.to_natural_language()}")
        
    except Exception as e:
        print(f"❌ PatientInfo 测试失败: {e}")
    
    try:
        from utils.pdf_generator import HealthPlanPDFGenerator
        print("✅ PDF生成器导入成功")
        
    except ImportError as e:
        print(f"❌ PDF生成器导入失败: {e}")
        print("💡 可能需要安装 reportlab: pip install reportlab")
    except Exception as e:
        print(f"❌ PDF生成器测试失败: {e}")

def test_basic_functionality():
    """测试基本功能"""
    print("\n" + "="*50)
    print("🧪 基本功能测试")
    print("="*50)
    
    test_imports()

if __name__ == "__main__":
    test_basic_functionality()
