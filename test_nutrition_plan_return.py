#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试返回 NutritionPlan 类的功能
"""

import asyncio
from model import PatientInfo
from routers.plan import generate, generate_with_response

async def test_nutrition_plan_return():
    """测试直接返回 NutritionPlan 对象"""
    
    print("🧪 测试返回 NutritionPlan 类")
    print("="*50)
    
    # 创建测试患者
    patient = PatientInfo(
        gender=0,  # 女性
        age=32,
        height=1.65,
        weight=60.0,
        pla=1.4,
        tags=["高血压", "2型糖尿病", "高血脂"]
    )
    
    print("👤 患者信息：")
    print(patient.to_natural_language())
    print()
    
    try:
        print("🔄 方案1：直接返回 NutritionPlan 对象")
        print("-" * 30)
        
        # 调用返回 NutritionPlan 的函数
        nutrition_plan = await generate(req=patient, generate_pdf=False)
        
        print(f"✅ 返回类型：{type(nutrition_plan).__name__}")
        print(f"📊 每日能量需求：{nutrition_plan.intake} 千卡")
        print(f"🥗 营养素建议数量：{len(nutrition_plan.meal)} 项")
        print(f"🍽️  膳食结构：{len(nutrition_plan.structure)} 餐")
        print(f"📝 膳食原则：{nutrition_plan.overview[:100]}...")
        
        # 展示营养素建议
        print("\n🥗 营养素建议详情：")
        for nutrient in nutrition_plan.meal:
            print(f"   • {nutrient.name}: {nutrient.amount}克 ({nutrient.ratio})")
        
        # 展示膳食结构
        print("\n🍽️  膳食结构详情：")
        for meal in nutrition_plan.structure:
            print(f"   • {meal.name}:")
            for item in meal.items:
                print(f"     - {item.type}: {item.amount}克 ({item.source})")
        
        print("\n" + "="*50)
        print("🔄 方案2：返回完整响应对象")
        print("-" * 30)
        
        # 调用返回完整响应的函数
        response = await generate_with_response(req=patient, generate_pdf=True)
        
        print(f"✅ 返回类型：{type(response).__name__}")
        print(f"📄 响应消息：{response.message}")
        print(f"📁 PDF路径：{response.pdf_path}")
        print(f"📊 营养方案类型：{type(response.nutrition_plan).__name__}")
        
        # 可以直接访问 NutritionPlan 对象
        plan = response.nutrition_plan
        print(f"📊 每日能量需求：{plan.intake} 千卡")
        
    except Exception as e:
        print(f"❌ 测试失败：{e}")
        print("💡 可能需要配置LLM服务或使用模拟数据")

def create_sample_nutrition_plan() -> 'NutritionPlan':
    """创建示例 NutritionPlan 对象（用于演示）"""
    from routers.plan.nutrition import NutritionPlan, MealStructureAdvice, MealStructureAdviceFood
    from routers.plan.calc import Nutrient
    
    # 营养素建议
    nutrients = [
        Nutrient(name="蛋白质", amount="60-80", ratio="15%-20%"),
        Nutrient(name="脂肪", amount="40-60", ratio="20%-30%"),
        Nutrient(name="碳水化合物", amount="180-230", ratio="50%-65%")
    ]
    
    # 膳食结构建议
    breakfast = MealStructureAdvice(
        name="早餐",
        items=[
            MealStructureAdviceFood(type="全谷类", amount=50, source="燕麦片"),
            MealStructureAdviceFood(type="乳制品", amount=200, source="低脂牛奶"),
            MealStructureAdviceFood(type="水果", amount=150, source="苹果")
        ]
    )
    
    lunch = MealStructureAdvice(
        name="午餐",
        items=[
            MealStructureAdviceFood(type="全谷类", amount=80, source="糙米饭"),
            MealStructureAdviceFood(type="水产品", amount=100, source="三文鱼"),
            MealStructureAdviceFood(type="蔬菜", amount=200, source="西兰花")
        ]
    )
    
    dinner = MealStructureAdvice(
        name="晚餐",
        items=[
            MealStructureAdviceFood(type="谷类", amount=60, source="全麦面条"),
            MealStructureAdviceFood(type="畜禽肉类", amount=80, source="鸡胸肉"),
            MealStructureAdviceFood(type="蔬菜", amount=250, source="菠菜")
        ]
    )
    
    # 创建营养方案
    nutrition_plan = NutritionPlan(
        overview="采用DASH饮食结合地中海饮食模式，短期目标为改善血脂和血糖水平...",
        intake=1800,
        meal=nutrients,
        structure=[breakfast, lunch, dinner],
        note="每日钠摄入<2000mg，每日饮水1500-2000ml，避免饮酒。"
    )
    
    return nutrition_plan

def test_nutrition_plan_usage():
    """演示如何使用 NutritionPlan 对象"""
    
    print("\n🎯 NutritionPlan 对象使用演示")
    print("="*50)
    
    # 创建示例营养方案
    plan = create_sample_nutrition_plan()
    
    print(f"📊 营养方案类型：{type(plan).__name__}")
    print(f"📊 每日能量需求：{plan.intake} 千卡")
    
    # 访问营养素建议
    print("\n🥗 营养素建议：")
    for nutrient in plan.meal:
        print(f"   • {nutrient.name}: {nutrient.amount}克 ({nutrient.ratio})")
    
    # 访问膳食结构
    print("\n🍽️  膳食结构：")
    for meal in plan.structure:
        print(f"   • {meal.name}: {len(meal.items)} 种食物")
    
    # 转换为字典（用于JSON序列化）
    plan_dict = plan.model_dump()
    print(f"\n📄 转换为字典后的键：{list(plan_dict.keys())}")
    
    # 转换为JSON
    plan_json = plan.model_dump_json(ensure_ascii=False, indent=2)
    print(f"\n📄 JSON长度：{len(plan_json)} 字符")
    
    print("\n✅ NutritionPlan 对象使用演示完成！")

if __name__ == "__main__":
    # 运行异步测试
    print("🚀 开始测试...")
    
    # 先演示 NutritionPlan 对象的基本使用
    test_nutrition_plan_usage()
    
    # 然后测试异步函数（如果LLM服务可用）
    # asyncio.run(test_nutrition_plan_return())
