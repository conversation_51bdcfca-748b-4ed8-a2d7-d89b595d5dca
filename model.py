from typing import Literal, List, Optional

from pydantic import BaseModel, Field, ConfigDict


class PatientInfo(BaseModel):
    id: str = Field(default="test001", description="患者的唯一标识符")
    gender: Literal["M", "F"] = Field(..., description="性别字段，M：男，F：女")
    age: int = Field(..., gt=0, le=120, description="年龄，必须大于0且小于等于120")
    height: float = Field(..., gt=0, le=3.0, description="身高，单位：米，必须大于0且小于等于3.0米")
    weight: float = Field(..., gt=0, le=500, description="体重，单位：公斤，必须大于0且小于等于500公斤")
    pla: float = Field(1.6, gt=0, le=5.0, description="体力活动水平，默认值为1.6，范围0-5.0")
    tags: List[str] = Field(..., description="患者的慢病类型和具体情况，如高血压、糖尿病、高血脂等")

    def to_natural_language(self) -> str:
        """
        将患者信息转换为自然语言描述，用于传入大模型

        Returns:
            str: 患者信息的自然语言描述
        """
        # 性别转换
        gender_text = "男性" if self.gender == "M" else "女性"

        # 身高转换为厘米显示
        height_cm = int(self.height * 100)

        # 体力活动水平描述
        if self.pla <= 1.2:
            activity_level = "久坐少动：如办公室工作，很少运动"
        elif self.pla <= 1.4:
            activity_level = "轻度活动：如偶尔散步，轻微家务"
        elif self.pla <= 1.6:
            activity_level = "中等活动：如每周2-3次运动"
        elif self.pla <= 1.9:
            activity_level = "重度活动：如每天运动或体力劳动"
        else:
            activity_level = "极重度活动：如专业运动员或重体力劳动"

        # 计算BMI并给出体重状态描述
        bmi = self.weight / (self.height ** 2)
        if bmi < 18.5:
            weight_status = "体重偏轻"
        elif bmi < 24:
            weight_status = "体重正常"
        elif bmi < 28:
            weight_status = "超重"
        else:
            weight_status = "肥胖"

        # 年龄段描述
        if self.age < 18:
            age_group = "青少年"
        elif self.age < 30:
            age_group = "青年"
        elif self.age < 50:
            age_group = "中年"
        elif self.age < 65:
            age_group = "中老年"
        else:
            age_group = "老年"

        # 慢病标签处理
        if self.tags:
            disease_info = f"，患有{', '.join(self.tags)}"
        else:
            disease_info = "，目前无明确慢性疾病"

        # 组合完整描述
        description = (
            f"患者为{self.age}岁{gender_text}（{age_group}），"
            f"身高{height_cm}厘米，体重{self.weight}公斤，"
            f"BMI为{bmi:.1f}（{weight_status}），"
            f"体力活动水平为{self.pla}（{activity_level}）"
            f"{disease_info}。"
        )

        return description

if __name__ == "__main__":
    print(PatientInfo(gender="F", age=32, height=1.65, weight=60.0, pla=1.4, tags=["高脂血症中危", "糖尿病"]).to_natural_language())