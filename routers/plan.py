from pydantic import BaseModel, Field
from typing import Literal

class GenerateReq(BaseModel):
    gender: Literal[0, 1] = Field(..., description="性别字段，1为男，0为女")
    age: int = Field(..., gt=0, le=120, description="年龄，必须大于0且小于等于120")
    height: float = Field(..., gt=0, le=3.0, description="身高，单位：米，必须大于0且小于等于3.0米")
    weight: float = Field(..., gt=0, le=500, description="体重，单位：公斤，必须大于0且小于等于500公斤")
    pla: float = Field(1.6, gt=0, le=5.0, description="体力活动水平，默认值为1.6，范围0-5.0")

async def generate(req: GenerateReq):
    

