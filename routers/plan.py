from fastapi import HTTPException
from fastapi.responses import FileResponse
from pydantic import BaseModel, Field
from typing import Optional
import os
from datetime import datetime

from model import PatientInfo
from routers.plan.nutrition import generate_with_llm_and_pdf, NutritionPlan

class GenerateResponse(BaseModel):
    """生成方案响应模型"""
    nutrition_plan: NutritionPlan = Field(..., description="营养方案")
    pdf_path: Optional[str] = Field(None, description="PDF文件路径")
    message: str = Field(..., description="响应消息")

async def generate(req: PatientInfo, generate_pdf: bool = False) -> GenerateResponse:
    """
    生成健康管理方案

    Args:
        req: 患者信息
        generate_pdf: 是否生成PDF文档

    Returns:
        GenerateResponse: 包含营养方案和PDF路径的响应
    """
    try:
        # 生成营养方案和PDF
        nutrition_plan, pdf_path = generate_with_llm_and_pdf(
            patient=req,
            generate_pdf=generate_pdf,
            pdf_filename=f"健康管理方案_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf" if generate_pdf else None
        )

        message = "营养方案生成成功"
        if pdf_path:
            message += f"，PDF已保存至 {pdf_path}"

        return GenerateResponse(
            nutrition_plan=nutrition_plan,
            pdf_path=pdf_path,
            message=message
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"生成方案失败: {str(e)}")

async def download_pdf(pdf_filename: str):
    """
    下载PDF文件

    Args:
        pdf_filename: PDF文件名

    Returns:
        FileResponse: PDF文件响应
    """
    if not os.path.exists(pdf_filename):
        raise HTTPException(status_code=404, detail="PDF文件不存在")

    return FileResponse(
        path=pdf_filename,
        filename=pdf_filename,
        media_type='application/pdf'
    )

