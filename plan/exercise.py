from typing import Literal

from pydantic import BaseModel, Field

template = """
你是一名专业的运动医学医生，请根据患者的基本信息、疾病情况以及对应的运动干预方案，为其制定个性化的运动处方建议。
**注意**
1. 生成内容需要参考患者基本信息和疾病情况，不能出现与之相悖的内容。
2. 生成内容需要参考患者的每日膳食能量摄入范围及营养素建议，不能出现与之相悖的内容。
3. **膳食原则及目标：** 采用以特殊目标维度为主的平衡膳食模式（如地中海饮食、DASH饮食等）。
- 短期目标： 改善某项具体指标或症状（如改善血糖、血脂、血压等）；
- 中期目标： 达到并维持某一健康状态（如减重、改善胰岛素敏感性等）；
- 长期目标： 降低某种疾病风险或延缓疾病进展（如降低心血管事件风险、延缓糖尿病并发症等）；
- 建议烹调方式： 推荐适合该疾病的合理烹饪方法（如蒸、煮、炖、烤等，避免油炸、烧烤）；
- 食物选择建议： 明确适宜摄入的食物类别及应限制或避免的食物类别。
示例参考：
    坚持地中海饮食为主的平衡膳食模式，短期目标为改善血脂水平，中期目标为达到并维持健康体重，长期目标为降低心血管疾病的发生风险。建议进餐应定时定量，多采用蒸、煮等方式烹调，减少油炸、烧烤；推荐多摄入全谷物、深海鱼类、坚果、橄榄油，适量控制红肉摄入。

以下是患者的基本信息：
```
{patient}
```

以下是患者的运动干预方案：
```
{nutrients}
```

{format_instructions}
"""

class ExerciseComponent(BaseModel):
    type: Literal["有氧运动", "抗阻运动", "柔韧性训练", "平衡训练"] = Field(..., description="运动类型")
    intensity: Literal["低", "中", "高"] = Field(..., description="强度")
    proportion: str = Field(..., description="占比")
    frequency: str = Field(..., description="频次")
    duration: str = Field(..., description="时长")
    notes: str | None = Field(None, description="注意事项")

class RxExercise(BaseModel):
    name: str = Field(..., description="运动处方名称")
    goal: str = Field(..., description="训练目标")
    components: List[RxComponent] = Field(..., min_items=1, description="训练组成")
    avoid: List[str] = Field(default=[], description="避免运动")
    overall: str = Field(..., description="整体注意事项")