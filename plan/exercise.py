from typing import Literal, List

from langchain_core.output_parsers import JsonOutputParser
from langchain_core.prompts import PromptTemplate
from langchain_core.runnables import RunnablePassthrough
from pydantic import BaseModel, Field, field_validator

from model import PatientInfo
from utils import llm

template = """
你是一名专业的运动医学医生，请根据患者的基本信息、疾病情况以及对应的运动干预方案，为其制定个性化的运动处方建议。

**注意**
1. 生成内容需要参考患者基本信息和疾病情况，不能出现与之相悖的内容。
2. 生成内容需要参考患者的运动干预方案，不能出现与之相悖的内容。
3. **运动处方名称：** 根据患者的疾病情况和运动干预方案，为其制定个性化的运动处方名称。
4. 检查生成内容是否符合科学，避免出现前后不一致的内容。

以下是患者的基本信息：
```
{patient}
```

以下是患者的运动干预方案：
```
{exercise}
```

{format_instructions}
"""

class ExerciseComponent(BaseModel):
    type: Literal["有氧运动", "抗阻运动", "柔韧性训练", "平衡训练"] = Field(..., description="运动类型")
    intensity: Literal["低", "中", "高"] = Field(..., description="强度")
    proportion: str = Field(..., description="占比")
    frequency: str = Field(..., description="频次")
    duration: str = Field(..., description="时长")
    notes: str | None = Field(None, description="注意事项")

class Exercise(BaseModel):
    name: str = Field(..., description="运动处方名称")
    goal: str = Field(..., description="训练目标")
    components: List[ExerciseComponent] = Field(..., description="训练组成，至少包含1项运动")
    avoid: List[str] = Field(default=[], description="避免运动")
    overall: str = Field(..., description="整体注意事项")

parser = JsonOutputParser(pydantic_object=Exercise)
format_instructions = parser.get_format_instructions()

prompt = PromptTemplate(
    template=template,
    input_variables=["patient", "exercise"],
    partial_variables={"format_instructions": format_instructions}
)

def generate_with_llm(patient: PatientInfo, exercise: Exercise) -> Exercise:
    """
    使用大模型生成运动方案

    Args:
        patient: 患者信息
        exercise: 运动干预方案

    Returns:
        Exercise: 运动方案
    """
    chain = (
        {"patient": RunnablePassthrough(), "exercise": RunnablePassthrough()}
        | prompt
        | llm.qwen3_30b_a3b
        | parser
    )
    advice
    return chain.invoke({
        "patient": patient.to_natural_language(),
        "exercise": exercise.to_natural_language()
    })