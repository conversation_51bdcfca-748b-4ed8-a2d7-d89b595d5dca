from typing import Literal, List

from langchain_core.output_parsers import JsonOutputParser
from langchain_core.prompts import PromptTemplate
from langchain_core.runnables import RunnablePassthrough
from pydantic import BaseModel, Field, field_validator

from model import PatientInfo
from utils import llm
from exercise_plan import plans
from utils.llm import init_langsmith

template = """
你是一名专业的运动医学医生，请根据患者的基本信息、疾病情况以及给定的运动干预方案，为其制定个性化的运动处方建议。

✅ 要求：
1. 内容必须依据患者的基本信息与疾病情况，不得出现与其相悖的建议；
2. 内容必须参考提供的运动干预方案，不得偏离其原则与核心要求；
3. 运动处方名称需结合疾病特点和干预目标，具备针对性与专业性；
4. 请确保建议内容科学、合理，前后逻辑一致，避免冲突或矛盾。
5. **整体注意事项：** 关于运动细节的描述，但请不要出现关于饮食、营养的建议。如果有多条事项请用序号进行排列。

以下是患者的基本信息：
```
{patient}
```

以下给定的运动干预方案：
```
{exercise}
```

{format_instructions}
"""

class ExerciseComponent(BaseModel):
    type: Literal["有氧运动", "抗阻运动", "柔韧性训练", "平衡训练"] = Field(..., description="运动类型")
    intensity: Literal["低", "中", "高"] = Field(..., description="运动强度")
    # proportion: str = Field(..., description="占比")
    frequency: str = Field(..., description="运动频率")
    duration: str = Field(..., description="运动时间")
    notes: str | None = Field(None, description="注意事项")

class Exercise(BaseModel):
    name: str = Field(..., description="运动处方名称")
    goal: str = Field(..., description="训练目标")
    overview: str = Field(..., description="运动处方整体描述")
    components: List[ExerciseComponent] = Field(..., description="训练组成，至少包含1项运动")
    avoid: List[str] = Field(default=[], description="避免运动")
    overall: List[str] = Field(..., description="整体注意事项")

parser = JsonOutputParser(pydantic_object=Exercise)
format_instructions = parser.get_format_instructions()

prompt = PromptTemplate(
    template=template,
    input_variables=["patient", "exercise"],
    partial_variables={"format_instructions": format_instructions}
)

async def generate_with_llm(patient: PatientInfo) -> Exercise:
    """
    使用大模型生成运动方案

    Args:
        patient: 患者信息

    Returns:
        Exercise: 运动方案
    """
    chain = (
        {"patient": RunnablePassthrough(), "exercise": RunnablePassthrough()}
        | prompt
        | llm.local
        | parser
    )
    suggestion_text = ""
    for tag in patient.tags:
        if plans.get(tag):
            suggestion_text += plans[tag]

    return chain.invoke({
        "patient": patient.to_natural_language(),
        "exercise": suggestion_text
    })

def main():
    llm.init_langsmith("运动处方")
    patient = PatientInfo(
        id="test001",
        gender="M",
        age=68,
        height=1.71,
        weight=81.3,
        pla=1.4,
        tags=["糖尿病","高脂血症低危"]
    )
    exercise_plan = generate_with_llm(patient)
    print(exercise_plan)

if __name__ == "__main__":
    init_langsmith("健康管理方案")
    main()