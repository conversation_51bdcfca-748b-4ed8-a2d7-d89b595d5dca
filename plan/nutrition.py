from typing import List, Optional, Literal

from langchain_core.output_parsers import JsonOutputParser
from langchain_core.prompts import PromptTemplate
from langchain_core.runnables import RunnablePassthrough
from pydantic import BaseModel, Field, ConfigDict

from model import PatientInfo
from nutrition_calculator import Nutrient, calc_all
from utils import llm
from utils.llm import init_langsmith

template = """
你是一名专业的注册营养师，请根据患者的基本信息、疾病情况和每日膳食能量摄入范围及营养素建议，为其制定个性化的营养方案和一日三餐膳食结构建议。
**注意**
1. 生成内容需要参考患者基本信息和疾病情况，不能出现与之相悖的内容。
2. 生成内容需要参考患者的每日膳食能量摄入范围及营养素建议，不能出现与之相悖的内容。
3. **膳食原则及目标：** 采用以特殊目标维度为主的平衡膳食模式（如地中海饮食、DASH饮食等）。
- 短期目标： 改善某项具体指标或症状（如改善血糖、血脂、血压等）；
- 中期目标： 达到并维持某一健康状态（如减重、改善胰岛素敏感性等）；
- 长期目标： 降低某种疾病风险或延缓疾病进展（如降低心血管事件风险、延缓糖尿病并发症等）；
- 建议烹调方式： 推荐适合该疾病的合理烹饪方法（如蒸、煮、炖、烤等，避免油炸、烧烤）；
- 食物选择建议： 明确适宜摄入的食物类别及应限制或避免的食物类别。
示例参考：
    坚持地中海饮食为主的平衡膳食模式，短期目标为改善血脂水平，中期目标为达到并维持健康体重，长期目标为降低心血管疾病的发生风险。建议进餐应定时定量，多采用蒸、煮等方式烹调，减少油炸、烧烤；推荐多摄入全谷物、深海鱼类、坚果、橄榄油，适量控制红肉摄入。

以下是患者的基本信息：
```
{patient}
```

以下是患者的每日膳食能量摄入范围及营养素建议：
```
{nutrients}
```

{format_instructions}
"""

class MealStructureAdviceFood(BaseModel):
    type: Literal[
        "畜禽肉类", "大豆和坚果", "蛋类", "低血糖生成指数食物", "谷类", "烹调用盐", "烹调用油", "全谷类", "乳制品", "蔬菜", "薯类", "水产品", "水果"] = Field(
        ..., description="食物总类")
    amount: int = Field(..., description="推荐量，单位为克（g）")
    source: str = Field(..., description="食物来源")

class MealStructureAdvice(BaseModel):
    model_config = ConfigDict(extra='ignore')
    name: Literal["早餐", "上午加餐", "午餐", "下午加餐", "晚餐"] = Field(..., description="餐次名称")
    items: List[MealStructureAdviceFood] = Field(..., description="该餐次的食物建议列表")

class NutritionPlan(BaseModel):
    overview: str = Field(..., description="膳食原则及目标")
    intake: int = Field(..., description="一日膳食能量需要量，单位千卡")
    meal: List[Nutrient] = Field(..., description="一日三餐营养素建议")
    structure: List[MealStructureAdvice] = Field(..., description="一日三餐膳食结构建议")
    note: Optional[str] = Field(None, description="其他注意事项")

parser = JsonOutputParser(pydantic_object=NutritionPlan)
format_instructions = parser.get_format_instructions()

prompt = PromptTemplate(
    template=template,
    input_variables=["patient", "nutrients"],
    partial_variables={"format_instructions": format_instructions}
)

async def generate_with_llm(patient: PatientInfo) -> NutritionPlan:
    """
    使用大模型生成营养方案

    Args:
        patient: 患者信息

    Returns:
        NutritionPlan: 营养方案
    """
    nutrients = calc_all(patient.gender, patient.age, patient.height, patient.weight, patient.pla)
    chain = (
        {"patient": RunnablePassthrough(), "nutrients": RunnablePassthrough()}
        | prompt
        | llm.qwen3_30b_a3b
        | parser
    )
    return await chain.ainvoke({
        "patient": patient.to_natural_language(),
        "nutrients": nutrients.to_natural_language()
    })


def main():
    """主函数 - 演示营养方案生成和PDF导出"""
    init_langsmith("健康管理方案")

    # 创建示例患者
    patient = PatientInfo(
        gender=0,
        age=32,
        height=1.65,
        weight=60.0,
        pla=1.4,
        tags=["高血压", "2型糖尿病", "高血脂"]
    )

    # 生成营养方案
    nutrition_plan = generate_with_llm(patient)
    print(nutrition_plan)


if __name__ == "__main__":
    main()