from typing import Dict, Any
import asyncio
from model import PatientInfo
import nutrition
import exercise
from utils.pdf_generator import PDFGenerator
from utils.llm import init_langsmith

patients = [
    PatientInfo(id="361261", gender=0, age=68, height=1.71, weight=81.0, pla=1.4, tags=["高脂血症低危", "糖尿病"]),
]

async def generate_plan(patient: PatientInfo) -> Dict[str, Any]:
    """
    并发生成营养方案和运动方案

    Args:
        patient: 患者信息

    Returns:
        包含营养方案和运动方案的字典
    """
    # 并发执行营养方案和运动方案的生成
    nutrition_task = asyncio.create_task(nutrition.generate_with_llm(patient))
    exercise_task = asyncio.create_task(exercise.generate_with_llm(patient))

    # 等待所有任务完成
    nutrition_plan, exercise_plan = await asyncio.gather(nutrition_task, exercise_task)

    return {
        "nutrition": nutrition_plan,
        "exercise": exercise_plan
    }

async def main():
    """主函数 - 演示健康管理方案生成和PDF导出"""
    init_langsmith("健康管理方案")
    
    # 使用异步方式生成方案
    for patient in patients:
        plan = await generate_plan(patient)
        print(plan)
        
        # 生成PDF
        pdf_generator = PDFGenerator(f"output/健康管理方案_{patient.id}.pdf")
        pdf_generator.generate_health_plan(
            patient_info=patient.model_dump(),
            nutrition_plan=plan["nutrition"],
            exercise_plan=plan["exercise"]
        )

if __name__ == "__main__":
    asyncio.run(main())
