import json
from typing import List
from pydantic import BaseModel, Field
from langchain_core.prompts import PromptTemplate
from langchain_core.output_parsers import JsonOutputParser
from langchain_core.runnables import RunnablePassthrough

from utils.llm import init_langsmith, local, qwen3_235b_a22b

class RxComponent(BaseModel):
    type: str = Field(..., description="类型", enum=["有氧运动", "抗阻运动", "柔韧性训练", "平衡训练"])
    intensity: str = Field(..., description="强度", enum=["低", "中", "高"])
    proportion: str = Field(..., description="占比")
    frequency: str = Field(..., description="频次")
    duration: str = Field(..., description="时长")
    notes: str | None = Field(None, description="注意事项")

class RxExercise(BaseModel):
    name: str = Field(..., description="运动处方名称")
    goal: str = Field(..., description="训练目标")
    components: List[RxComponent] = Field(..., min_items=1, description="训练组成")
    avoid: List[str] = Field(default=[], description="避免运动")
    overall: str = Field(..., description="整体注意事项")

template = """
    您是一名优秀的运动处方专家，请根据用户提供的信息，开具科学、合理、个性化的运动处方。

    以下是用户提供的信息：
    {query}

    请严格遵循以下格式输出JSON：
    {format_instructions}
"""

parser = JsonOutputParser(pydantic_object=RxExercise)
prompt = PromptTemplate(
    template=template,
    input_variables=["query"],
    partial_variables={"format_instructions": parser.get_format_instructions()}
)

def process(query: str):
    chain = (
        {"query": RunnablePassthrough()}
        | prompt
        | local
        | parser
    )
    result = chain.invoke(query)
    print(json.dumps(result, ensure_ascii=False, indent=2))  # 直接打印字典

if __name__ == "__main__":
    init_langsmith("iFollow")
    process("高血脂症（低危）。低风险人群10年ASCVD风险 <5%，预防危险因素的发生，健康促进为主。干预策略：每周150分钟中等强度有氧运动")

