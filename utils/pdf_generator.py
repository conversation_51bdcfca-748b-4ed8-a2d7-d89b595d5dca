#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
PDF生成器 - 将健康管理方案转换为美观的PDF文档
"""

import os
from datetime import datetime
from typing import Dict, Any, List
from reportlab.lib.pagesizes import A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import cm
from reportlab.lib.colors import HexColor, black, white
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from reportlab.lib.enums import TA_CENTER, TA_JUSTIFY
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont

from model import PatientInfo
from plan.nutrition import NutritionPlan

class HealthPlanPDFGenerator:
    """健康管理方案PDF生成器"""
    
    def __init__(self):
        self.setup_fonts()
        self.setup_styles()
        
    def setup_fonts(self):
        """设置中文字体"""
        try:
            # 尝试注册中文字体（需要系统中有这些字体）
            font_paths = [
                "C:/Windows/Fonts/simhei.ttf",  # 黑体
                "C:/Windows/Fonts/simsun.ttc",  # 宋体
                "/System/Library/Fonts/PingFang.ttc",  # macOS
                "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf"  # Linux
            ]
            
            for font_path in font_paths:
                if os.path.exists(font_path):
                    pdfmetrics.registerFont(TTFont('Chinese', font_path))
                    break
        except:
            # 如果无法注册中文字体，使用默认字体
            pass
    
    def setup_styles(self):
        """设置文档样式"""
        self.styles = getSampleStyleSheet()
        
        # 标题样式
        self.styles.add(ParagraphStyle(
            name='ChineseTitle',
            parent=self.styles['Title'],
            fontName='Chinese' if 'Chinese' in pdfmetrics.getRegisteredFontNames() else 'Helvetica-Bold',
            fontSize=24,
            spaceAfter=30,
            alignment=TA_CENTER,
            textColor=HexColor('#2E86AB')
        ))
        
        # 章节标题样式
        self.styles.add(ParagraphStyle(
            name='ChineseHeading1',
            parent=self.styles['Heading1'],
            fontName='Chinese' if 'Chinese' in pdfmetrics.getRegisteredFontNames() else 'Helvetica-Bold',
            fontSize=18,
            spaceAfter=12,
            spaceBefore=20,
            textColor=HexColor('#A23B72')
        ))
        
        # 子标题样式
        self.styles.add(ParagraphStyle(
            name='ChineseHeading2',
            parent=self.styles['Heading2'],
            fontName='Chinese' if 'Chinese' in pdfmetrics.getRegisteredFontNames() else 'Helvetica-Bold',
            fontSize=14,
            spaceAfter=8,
            spaceBefore=12,
            textColor=HexColor('#F18F01')
        ))
        
        # 正文样式
        self.styles.add(ParagraphStyle(
            name='ChineseNormal',
            parent=self.styles['Normal'],
            fontName='Chinese' if 'Chinese' in pdfmetrics.getRegisteredFontNames() else 'Helvetica',
            fontSize=12,
            spaceAfter=6,
            alignment=TA_JUSTIFY,
            leading=18
        ))
        
        # 患者信息样式
        self.styles.add(ParagraphStyle(
            name='PatientInfo',
            parent=self.styles['Normal'],
            fontName='Chinese' if 'Chinese' in pdfmetrics.getRegisteredFontNames() else 'Helvetica',
            fontSize=11,
            spaceAfter=4,
            leftIndent=20,
            textColor=HexColor('#555555')
        ))
    
    def generate_pdf(self, patient: PatientInfo, nutrition_plan: NutritionPlan, 
                    exercise_plan: Dict[str, Any] = None, filename: str = None) -> str:
        """
        生成健康管理方案PDF
        
        Args:
            patient: 患者信息
            nutrition_plan: 营养方案
            exercise_plan: 运动方案（可选）
            filename: 输出文件名（可选）
            
        Returns:
            str: 生成的PDF文件路径
        """
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"健康管理方案_{timestamp}.pdf"
        
        # 创建PDF文档
        doc = SimpleDocTemplate(
            filename,
            pagesize=A4,
            rightMargin=2*cm,
            leftMargin=2*cm,
            topMargin=2*cm,
            bottomMargin=2*cm
        )
        
        # 构建文档内容
        story = []
        
        # 添加标题
        story.append(Paragraph("个性化健康管理方案", self.styles['ChineseTitle']))
        story.append(Spacer(1, 20))
        
        # 添加生成日期
        date_str = datetime.now().strftime("%Y年%m月%d日")
        story.append(Paragraph(f"生成日期：{date_str}", self.styles['ChineseNormal']))
        story.append(Spacer(1, 30))
        
        # 添加患者信息
        self._add_patient_info(story, patient)
        
        # 添加营养方案
        self._add_nutrition_plan(story, nutrition_plan)
        
        # 添加运动方案（如果有）
        if exercise_plan:
            self._add_exercise_plan(story, exercise_plan)
        
        # 添加免责声明
        self._add_disclaimer(story)
        
        # 生成PDF
        doc.build(story)
        
        return filename
    
    def _add_patient_info(self, story: List, patient: PatientInfo):
        """添加患者信息部分"""
        story.append(Paragraph("一、患者基本信息", self.styles['ChineseHeading1']))
        
        # 患者信息表格
        patient_data = [
            ['性别', '男性' if patient.gender == 1 else '女性'],
            ['年龄', f'{patient.age}岁'],
            ['身高', f'{int(patient.height * 100)}厘米'],
            ['体重', f'{patient.weight}公斤'],
            ['BMI', f'{patient.weight / (patient.height ** 2):.1f}'],
            ['体力活动水平', f'{patient.pla}'],
            ['疾病标签', ', '.join(patient.tags) if patient.tags else '无']
        ]
        
        table = Table(patient_data, colWidths=[3*cm, 8*cm])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), HexColor('#F0F0F0')),
            ('TEXTCOLOR', (0, 0), (-1, -1), black),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, -1), 'Chinese' if 'Chinese' in pdfmetrics.getRegisteredFontNames() else 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 11),
            ('GRID', (0, 0), (-1, -1), 1, black),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('ROWBACKGROUNDS', (0, 0), (-1, -1), [white, HexColor('#F8F8F8')])
        ]))
        
        story.append(table)
        story.append(Spacer(1, 20))
    
    def _add_nutrition_plan(self, story: List, nutrition_plan: NutritionPlan):
        """添加营养方案部分"""
        story.append(Paragraph("二、营养管理方案", self.styles['ChineseHeading1']))
        
        # 膳食原则及目标
        story.append(Paragraph("1. 膳食原则及目标", self.styles['ChineseHeading2']))
        story.append(Paragraph(nutrition_plan.overview, self.styles['ChineseNormal']))
        story.append(Spacer(1, 15))
        
        # 每日能量摄入
        story.append(Paragraph("2. 每日能量需求", self.styles['ChineseHeading2']))
        story.append(Paragraph(f"建议每日能量摄入：{nutrition_plan.intake} 千卡", self.styles['ChineseNormal']))
        story.append(Spacer(1, 15))
        
        # 营养素建议
        story.append(Paragraph("3. 营养素建议", self.styles['ChineseHeading2']))
        
        nutrient_data = [['营养素', '推荐摄入量', '占总能量比例']]
        for nutrient in nutrition_plan.meal:
            nutrient_data.append([
                nutrient.name,
                f"{nutrient.amount}克" if isinstance(nutrient.amount, (int, float)) else nutrient.amount,
                nutrient.ratio
            ])
        
        nutrient_table = Table(nutrient_data, colWidths=[4*cm, 4*cm, 4*cm])
        nutrient_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), HexColor('#4CAF50')),
            ('TEXTCOLOR', (0, 0), (-1, 0), white),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, -1), 'Chinese' if 'Chinese' in pdfmetrics.getRegisteredFontNames() else 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 11),
            ('GRID', (0, 0), (-1, -1), 1, black),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [white, HexColor('#F8F8F8')])
        ]))
        
        story.append(nutrient_table)
        story.append(Spacer(1, 15))
        
        # 膳食结构建议
        story.append(Paragraph("4. 一日三餐膳食结构建议", self.styles['ChineseHeading2']))
        
        for meal in nutrition_plan.structure:
            story.append(Paragraph(f"• {meal.name}", self.styles['ChineseHeading2']))
            
            meal_data = [['食物类别', '推荐量', '食物来源']]
            for item in meal.items:
                meal_data.append([item.type, f"{item.amount}克", item.source])
            
            meal_table = Table(meal_data, colWidths=[4*cm, 3*cm, 5*cm])
            meal_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), HexColor('#FF9800')),
                ('TEXTCOLOR', (0, 0), (-1, 0), white),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, -1), 'Chinese' if 'Chinese' in pdfmetrics.getRegisteredFontNames() else 'Helvetica'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('GRID', (0, 0), (-1, -1), 1, black),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('ROWBACKGROUNDS', (0, 1), (-1, -1), [white, HexColor('#FFF3E0')])
            ]))
            
            story.append(meal_table)
            story.append(Spacer(1, 10))
        
        # 其他注意事项
        if nutrition_plan.note:
            story.append(Paragraph("5. 其他注意事项", self.styles['ChineseHeading2']))
            story.append(Paragraph(nutrition_plan.note, self.styles['ChineseNormal']))
            story.append(Spacer(1, 20))
    
    def _add_exercise_plan(self, story: List, exercise_plan: Dict[str, Any]):
        """添加运动方案部分"""
        story.append(Paragraph("三、运动管理方案", self.styles['ChineseHeading1']))
        story.append(Paragraph("运动方案内容待完善...", self.styles['ChineseNormal']))
        story.append(Spacer(1, 20))
    
    def _add_disclaimer(self, story: List):
        """添加免责声明"""
        story.append(Paragraph("免责声明", self.styles['ChineseHeading1']))
        disclaimer_text = """
        本健康管理方案仅供参考，不能替代专业医疗建议。在开始任何新的饮食或运动计划之前，
        请咨询您的医生或注册营养师。个人健康状况因人而异，本方案的建议可能不适用于所有人。
        如有任何健康问题或疑虑，请及时就医。
        """
        story.append(Paragraph(disclaimer_text, self.styles['ChineseNormal']))


def generate_health_plan_pdf(patient: PatientInfo, nutrition_plan: NutritionPlan, 
                           exercise_plan: Dict[str, Any] = None, filename: str = None) -> str:
    """
    便捷函数：生成健康管理方案PDF
    
    Args:
        patient: 患者信息
        nutrition_plan: 营养方案
        exercise_plan: 运动方案（可选）
        filename: 输出文件名（可选）
        
    Returns:
        str: 生成的PDF文件路径
    """
    generator = HealthPlanPDFGenerator()
    return generator.generate_pdf(patient, nutrition_plan, exercise_plan, filename)


if __name__ == "__main__":
    # 测试示例

    # 创建测试患者
    test_patient = PatientInfo(
        gender=0,
        age=32,
        height=1.65,
        weight=60.0,
        pla=1.4,
        tags=["高血压", "2型糖尿病", "高血脂"]
    )
    
    print("正在生成营养方案...")
    # 注意：这需要LLM服务可用
    # nutrition_plan = generate_with_llm(test_patient)
    
    print("正在生成PDF...")
    # pdf_path = generate_health_plan_pdf(test_patient, nutrition_plan)
    # print(f"PDF已生成：{pdf_path}")
