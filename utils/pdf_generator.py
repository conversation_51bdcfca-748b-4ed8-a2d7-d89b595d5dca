from reportlab.lib import colors
from reportlab.lib.pagesizes import A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch, cm
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, PageBreak
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import os
import sys

# 定义颜色
COLORS = {
    'primary': colors.HexColor('#2C3E50'),  # 深蓝灰色
    'secondary': colors.HexColor('#3498DB'),  # 蓝色
    'accent': colors.HexColor('#E74C3C'),  # 红色
    'light': colors.HexColor('#ECF0F1'),  # 浅灰色
    'dark': colors.HexColor('#2C3E50'),  # 深色
    'success': colors.HexColor('#27AE60'),  # 绿色
    'warning': colors.HexColor('#F39C12'),  # 橙色
    'text': colors.HexColor('#2C3E50'),  # 文本颜色
    'background': colors.HexColor('#FFFFFF'),  # 背景色
}

# 注册系统宋体
font_path = "C:\\Windows\\Fonts\\simsun.ttc"
if not os.path.exists(font_path):
    print(f"错误：找不到字体文件 {font_path}")
    sys.exit(1)

try:
    pdfmetrics.registerFont(TTFont("SimSun", font_path))
except Exception as e:
    print(f"错误：注册字体失败 - {str(e)}")
    sys.exit(1)

class PDFGenerator:
    def __init__(self, output_path: str):
        self.output_path = output_path
        self.styles = getSampleStyleSheet()
        self._setup_styles()
        
    def _setup_styles(self):
        """设置中文字体样式"""
        # 标题样式
        self.styles.add(ParagraphStyle(
            name='ChineseTitle',
            fontName='SimSun',
            fontSize=24,
            leading=28,
            textColor=COLORS['primary'],
            spaceAfter=20,
            alignment=1,  # 居中
        ))
        
        # 副标题样式
        self.styles.add(ParagraphStyle(
            name='ChineseSubtitle',
            fontName='SimSun',
            fontSize=18,
            leading=22,
            textColor=COLORS['secondary'],
            spaceBefore=15,
            spaceAfter=10,
            borderWidth=0,
            borderColor=COLORS['secondary'],
            borderPadding=5,
            borderRadius=5,
        ))
        
        # 正文样式
        self.styles.add(ParagraphStyle(
            name='Chinese',
            fontName='SimSun',
            fontSize=12,
            leading=16,
            textColor=COLORS['text'],
            spaceBefore=6,
            spaceAfter=6,
        ))
        
        # 加粗样式
        self.styles.add(ParagraphStyle(
            name='ChineseBold',
            fontName='SimSun',
            fontSize=14,
            leading=18,
            textColor=COLORS['primary'],
            spaceBefore=8,
            spaceAfter=8,
        ))
        
        # 列表项样式
        self.styles.add(ParagraphStyle(
            name='ChineseList',
            fontName='SimSun',
            fontSize=12,
            leading=16,
            textColor=COLORS['text'],
            leftIndent=20,
            spaceBefore=3,
            spaceAfter=3,
        ))

    def _create_section_header(self, title: str) -> list:
        """创建带样式的章节标题"""
        elements = []
        elements.append(Paragraph(title, self.styles['ChineseSubtitle']))
        elements.append(Spacer(1, 10))
        return elements

    def _create_table_style(self, header_bg_color=COLORS['light']) -> TableStyle:
        """创建统一的表格样式"""
        return TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), 'SimSun'),
            ('FONTSIZE', (0, 0), (-1, -1), 12),
            ('GRID', (0, 0), (-1, -1), 0.5, COLORS['light']),
            ('BACKGROUND', (0, 0), (-1, 0), header_bg_color),
            ('TEXTCOLOR', (0, 0), (-1, 0), COLORS['primary']),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('PADDING', (0, 0), (-1, -1), 8),
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, COLORS['light']]),
        ])

    def generate_health_plan(self, patient_info: dict, nutrition_plan: dict, exercise_plan: dict):
        """生成健康管理方案PDF"""
        try:
            # 确保输出目录存在
            output_dir = os.path.dirname(self.output_path)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir)
                print(f"创建输出目录：{output_dir}")

            doc = SimpleDocTemplate(
                self.output_path,
                pagesize=A4,
                rightMargin=2*cm,
                leftMargin=2*cm,
                topMargin=2*cm,
                bottomMargin=2*cm
            )

            story = [Paragraph("个性化健康管理方案", self.styles['ChineseTitle']), Spacer(1, 20)]
            
            # 添加标题

            # 添加患者基本信息
            story.extend(self._create_section_header("一、患者基本信息"))
            patient_data = [
                ["性别", "男" if patient_info.get("gender") == "M" else "女"],
                ["年龄", f"{patient_info.get('age', '未知')}岁"],
                ["身高", f"{patient_info.get('height', '未知')}米"],
                ["体重", f"{patient_info.get('weight', '未知')}公斤"],
                ["疾病标签", ", ".join(patient_info.get("tags", []))]
            ]
            patient_table = Table(patient_data, colWidths=[2*inch, 4*inch])
            patient_table.setStyle(self._create_table_style())
            story.append(patient_table)
            story.append(Spacer(1, 15))

            # 添加营养方案
            story.extend(self._create_section_header("二、营养方案"))
            # 将概述文本按句号分割，每句单独一行
            overview_text = nutrition_plan.get("overview", "")
            for sentence in overview_text.split("。"):
                if sentence.strip():  # 确保不是空字符串
                    story.append(Paragraph(sentence.strip() + "。", self.styles['Chinese']))
            story.append(Spacer(1, 10))

            # 添加营养素摄入建议
            story.append(Paragraph("每日营养素摄入建议：", self.styles['ChineseBold']))
            meal_data = [["营养素", "推荐量（g）", "占总能量比（%）"]]
            for meal in nutrition_plan.get("meal", []):
                meal_data.append([
                    meal.get("name", ""),
                    meal.get("amount", ""),
                    meal.get("ratio", "")
                ])
            meal_table = Table(meal_data, colWidths=[2*inch, 2*inch, 2*inch])
            meal_table.setStyle(self._create_table_style())
            story.append(meal_table)
            story.append(Spacer(1, 15))

            # 添加膳食结构建议
            story.append(Paragraph("膳食结构建议：", self.styles['ChineseBold']))
            for structure in nutrition_plan.get("structure", []):
                story.append(Paragraph(f"{structure.get('name', '')}：", self.styles['ChineseBold']))
                items_data = [["食物种类", "推荐量", "食物来源"]]
                for item in structure.get("items", []):
                    items_data.append([
                        item.get("type", ""),
                        f"{item.get('amount', '')}g",
                        item.get("source", "")
                    ])
                items_table = Table(items_data, colWidths=[2*inch, 2*inch, 2*inch])
                items_table.setStyle(self._create_table_style())
                story.append(items_table)
                story.append(Spacer(1, 10))

            # 添加营养方案注意事项
            if nutrition_plan.get("note"):
                story.append(Paragraph("注意事项：", self.styles['ChineseBold']))
                for i, item in enumerate(nutrition_plan.get("note", []), 1):
                    story.append(Paragraph(f"{i}. {item}", self.styles["ChineseList"]))
                story.append(Spacer(1, 15))

            # 添加运动方案
            story.extend(self._create_section_header("三、运动方案"))
            story.append(Paragraph(exercise_plan.get("name", ""), self.styles['Chinese']))
            story.append(Spacer(1, 10))

            # 添加运动处方整体描述
            if exercise_plan.get("overview"):
                story.append(Paragraph(exercise_plan.get("overview", ""), self.styles['Chinese']))
                story.append(Spacer(1, 10))

            # 添加运动目标
            if exercise_plan.get("goal"):
                story.append(Paragraph("运动目标：", self.styles['ChineseBold']))
                story.append(Paragraph(exercise_plan.get("goal", ""), self.styles['Chinese']))
                story.append(Spacer(1, 10))

            # 添加运动建议
            story.append(Paragraph("运动建议：", self.styles['ChineseBold']))
            for component in exercise_plan.get("components", []):
                # 运动类型加粗显示
                story.append(Paragraph(f"<b>{component.get('type', '')}</b>", self.styles['ChineseBold']))
                
                # 其他信息以序号形式展示
                details = [
                    f"1. 运动强度：{component.get('intensity', '')}",
                    f"2. 运动频率：{component.get('frequency', '')}",
                    f"3. 运动时间：{component.get('duration', '')}"
                ]
                
                # 如果有注意事项，添加为第4点
                if component.get("notes"):
                    details.append(f"4. 注意事项：{component.get('notes', '')}")
                
                # 添加所有详细信息
                for detail in details:
                    story.append(Paragraph(detail, self.styles['ChineseList']))
                
                story.append(Spacer(1, 10))

            # 添加需要避免的运动
            if exercise_plan.get("avoid"):
                story.append(Paragraph("需要避免的运动：", self.styles['ChineseBold']))
                for i, avoid_item in enumerate(exercise_plan.get("avoid", []), 1):
                    story.append(Paragraph(f"{i}. {avoid_item}", self.styles['ChineseList']))
                story.append(Spacer(1, 10))

            # 添加总体注意事项
            if exercise_plan.get("overall"):
                story.append(Paragraph("总体注意事项：", self.styles['ChineseBold']))
                for i, item in enumerate(exercise_plan.get("overall", []), 1):
                    story.append(Paragraph(f"{i}. {item}", self.styles["ChineseList"]))
                story.append(Spacer(1, 10))

            # 生成PDF
            print(f"正在生成PDF文件：{self.output_path}")
            doc.build(story)
            print(f"PDF文件生成成功：{self.output_path}")
            
            # 验证文件是否生成
            if os.path.exists(self.output_path):
                print(f"文件大小：{os.path.getsize(self.output_path)} 字节")
            else:
                print("错误：文件未生成")
                
        except Exception as e:
            print(f"生成PDF时发生错误：{str(e)}")
            raise

    @staticmethod
    def test_generate():
        """测试PDF生成"""
        try:
            # 测试数据
            patient_info = {
                "id": "test001",
                "gender": 0,
                "age": 45,
                "height": 1.75,
                "weight": 70.0,
                "tags": ["高血压", "糖尿病"]
            }

            nutrition_plan = {
                "overview": "采用地中海饮食为主的平衡膳食模式，短期目标为改善血糖和血脂水平，中期目标为达到并维持健康体重，长期目标为降低心血管疾病的发生风险。建议进餐定时定量，多采用蒸、煮、炖等烹调方式，减少油炸、烧烤；推荐多摄入全谷物、深海鱼类、坚果、橄榄油，适量控制红肉摄入。",
                "intake": 2256,
                "meal": [
                    {"name": "碳水化合物", "amount": "282-367g", "ratio": "50%-65%"},
                    {"name": "脂肪", "amount": "50-75g", "ratio": "20%-30%"},
                    {"name": "蛋白质", "amount": "85-113g", "ratio": "15%-20%"}
                ],
                "structure": [
                    {
                        "name": "早餐",
                        "items": [
                            {"type": "全谷类", "amount": 50, "source": "燕麦片"},
                            {"type": "乳制品", "amount": 200, "source": "无糖酸奶"},
                            {"type": "低血糖生成指数食物", "amount": 100, "source": "圣女果"},
                            {"type": "烹调用油", "amount": 5, "source": "橄榄油（拌沙拉）"}
                        ]
                    },
                    {
                        "name": "上午加餐",
                        "items": [
                            {"type": "大豆和坚果", "amount": 15, "source": "杏仁"}
                        ]
                    },
                    {
                        "name": "午餐",
                        "items": [
                            {"type": "全谷类", "amount": 100, "source": "糙米饭"},
                            {"type": "水产品", "amount": 150, "source": "清蒸鲈鱼"},
                            {"type": "蔬菜", "amount": 200, "source": "西兰花+菠菜"},
                            {"type": "烹调用油", "amount": 10, "source": "橄榄油（凉拌）"}
                        ]
                    },
                    {
                        "name": "下午加餐",
                        "items": [
                            {"type": "低血糖生成指数食物", "amount": 150, "source": "黄瓜条"}
                        ]
                    },
                    {
                        "name": "晚餐",
                        "items": [
                            {"type": "全谷类", "amount": 75, "source": "全麦面条"},
                            {"type": "畜禽肉类", "amount": 100, "source": "白切鸡（去皮）"},
                            {"type": "蔬菜", "amount": 250, "source": "混合生菜沙拉"},
                            {"type": "烹调用盐", "amount": 3, "source": "低钠盐"}
                        ]
                    }
                ],
                "note": "1.每日饮水1500-2000ml，避免含糖饮料；2.监测空腹及餐后血糖，根据血糖调整碳水化合物摄入；3.每周至少150分钟中等强度有氧运动（如快走）；4.避免动物内脏、油炸食品及精制糖类。"
            }

            exercise_plan = {
                "name": "糖尿病合并高脂血症综合管理运动处方",
                "overview": "本运动处方针对糖尿病合并高脂血症患者设计，以有氧运动为主，结合抗阻训练，通过科学合理的运动方案，帮助患者改善血糖血脂水平，控制体重，降低心血管风险。运动强度适中，频率合理，适合长期坚持。",
                "goal": "调控血糖血脂水平并实现体重管理，降低心血管风险，同时降低10年ASCVD风险至<5%",
                "components": [
                    {
                        "type": "有氧运动",
                        "intensity": "中",
                        "proportion": "60%",
                        "frequency": "每周5-7次",
                        "duration": "每次30-60分钟",
                        "notes": "优先选择快走、椭圆机或水中运动；通过动态血糖监测（CGM）确保运动前血糖>6.0mmol/L，佩戴心率带控制强度（最大心率50%-70%），RPE控制在11-13"
                    },
                    {
                        "type": "抗阻运动",
                        "intensity": "中",
                        "proportion": "40%",
                        "frequency": "每周3次",
                        "duration": "每次20-30分钟",
                        "notes": "采用弹力带、固定器械或自重训练（深蹲/臀桥/平板支撑），每组8-12次，2-3组/动作，避免憋气动作诱发血压骤升"
                    }
                ],
                "avoid": ["高强度间歇训练", "空腹运动", "大重量自由重量训练", "长时间静止站立"],
                "overall": "运动前需经医生评估（含糖化血红蛋白和足部神经检查）。运动前后进行5-10分钟动态拉伸和呼吸训练，随身携带快速碳水（如葡萄糖片）。建议联合低碳水饮食（每日碳水占比<45%）及低脂高纤维饮食，每周至少3次家庭血糖监测（运动前后重点监测）。出现自主神经病变者需进行运动负荷试验后再调整强度，足部感觉异常者优先选择非负重运动（如游泳/骑车）。定期监测血脂、血压及运动耐受性，逐步增加运动量（如延长单次运动时间至45分钟或增加频次至每周7次），同时结合日常活动减少久坐时间。"
            }

            # 生成PDF
            output_path = os.path.join("output", f"{patient_info['id']}.pdf")
            print(f"准备生成PDF文件：{output_path}")
            generator = PDFGenerator(output_path)
            generator.generate_health_plan(patient_info, nutrition_plan, exercise_plan)
            
        except Exception as e:
            print(f"测试生成PDF时发生错误：{str(e)}")
            raise

def main():
    """主函数"""
    try:
        # 确保输出目录存在
        output_dir = "output"
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
            print(f"创建输出目录：{output_dir}")
        
        # 执行测试
        print("开始执行PDF生成测试...")
        PDFGenerator.test_generate()
        print("测试完成")
        
    except Exception as e:
        print(f"程序执行出错：{str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main() 