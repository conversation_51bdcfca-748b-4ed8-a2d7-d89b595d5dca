from pydantic import BaseModel, Field
from typing import List


class Nutrient(BaseModel):
    name: str = Field(..., description="营养素名称，如蛋白质、脂肪等")
    amount: str = Field(..., description="推荐摄入量，单位为克（g）")
    ratio: str = Field(..., description="占每日总能量的比例，单位为百分比")

def calc(gender: int, age: int, height: float, weight: float, pla: float = 1.6) -> dict:
    """
        根据患者具体情况，计算一日膳食能量需要量和营养素建议。

        Args:
            gender: 性别字段，性别字段，必须是 1（男）或 0（女）
            age: 年龄
            height: 身高，单位：米
            weight: 体重，单位：公斤
            pla: 体力活动水平，默认值为 1.6

        Returns:
            dict: 包含能量需求和营养素建议的字典
    """
    # 计算每日能量需求量
    energy = calc_energy(age, gender, weight, height, pla)

    # 计算营养素建议
    nutrients = calc_nutrients(energy, age)

    # 返回计算结果字典
    return {
        "energy": energy,  # 每日能量需求量（千卡），四舍五入取整数
        "nutrients": nutrients  # 营养素建议，使用 Nutrient 类列表
    }

def calc_energy(age: int, gender: int, weight: float, height: float, pla: float):
    """ 计算一日膳食能量需要量 """
    eer = 0
    if age < 3:
        if gender == 1:
            eer = (0.118 * weight + 3.59 * height - 1.55) * 238.85 * pla * 1.01
        else:
            eer = (0.127 * weight + 2.94 * height - 1.2) * 238.85 * pla * 1.01
    elif 3 <= age < 10:
        if gender == 1:
            eer = (0.0632 * weight + 1.31 * height + 1.28) * 238.85 * pla * 1.01
        else:
            eer = (0.0666 * weight + 0.878 * height + 1.46) * 238.85 * pla * 1.01
    elif 10 <= age < 18:
        if gender == 1:
            eer = (0.0651 * weight + 1.11 * height + 1.25) * 238.85 * pla * 1.01
        else:
            eer = (0.0393 * weight + 1.04 * height + 1.93) * 238.85 * pla * 1.01
    elif 18 <= age < 50:
        eer = (14.52 * weight - 155.88 * gender + 565.79) * pla
    elif 50 <= age < 65:
        eer = (14.52 * weight - 155.88 * gender + 565.79) * pla * 0.95
    elif 65 <= age < 75:
        eer = (14.52 * weight - 155.88 * gender + 565.79) * pla * 0.925
    else:
        eer = (14.52 * weight - 155.88 * gender + 565.79) * pla * 0.9
    return round(eer)

def calc_nutrients(energy: int, age: int) -> List[Nutrient]:
    """
    计算一日三餐营养素建议

    Args:
        energy: 每日能量需求量（千卡）
        age: 年龄

    Returns:
        List[Nutrient]: 营养素建议列表
    """
    nutrients = []

    return nutrients

def calc_nutrients_carb(eer: int,age: int):
    name = "碳水化合物"
    if age < 1:
        return Nutrient(name=name, amount="80", ratio="50%-65%")
    else:
        return Nutrient(name=name, amount=f"{round(eer * 0.5 / 4)}-{round(eer * 0.65 / 4)}", ratio="50%-65%")

def calc_nutrients_fat(eer: int, age: int):
    name = "脂肪"
    if age < 1:
        return Nutrient(name=name, amount=f"{round(eer * 0.4 / 9)}", ratio="40%")
    elif 1 <= age < 4:
        return Nutrient(name=name, amount=f"{round(eer * 0.35 / 9)}", ratio="35%")
    else:
        return Nutrient(name=name, amount=f"{round(eer * 0.2 / 9)}-{round(eer * 0.3 / 9)}", ratio="20%-30%")

def calc_nutrients_protein(eer: int, age: int):
    name = "蛋白质"
    if age < 65:
        return Nutrient(name=name, amount=f"{round(eer * 0.1 / 4)}-{round(eer * 0.2 / 4)}", ratio="10%-20%")
    else:
        return Nutrient(name=name, amount=f"{round(eer * 0.15 / 4)}-{round(eer * 0.2 / 4)}", ratio="15%-20%")

def calc_nutrients_fiber():
    name = "膳食纤维"
    if age < 1:
        return Nutrient(name=name, amount="-", ratio="-")
    elif 1 <= age < 4:
        return Nutrient(name=name, amount="19", ratio="19g")
    elif 3 <= age < 8:
        return Nutrient(name=name, amount="25", ratio="25g")
    elif 8 <= age < 14:

if __name__ == "__main__":
    print(calc(0, 32, 1.6, 49.5))