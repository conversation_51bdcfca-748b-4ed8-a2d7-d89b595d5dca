
def calc(gender: int, age: int, height: float, weight: float, pla: float = 1.6) -> dict:
    """
        根据患者具体情况，计算一日膳食能量需要量和营养素建议。

        Args:
            gender: 性别字段，性别字段，必须是 1（男）或 0（女）
            age: 年龄
            height: 身高，单位：米
            weight: 体重，单位：公斤
            pla: 体力活动水平，默认值为 1.6
    """
    # 一日三餐营养素建议


    # 返回计算结果字典
    return {
        "energy": calc_energy(age, gender, weight, height, pla),  # 每日能量需求量（千卡），四舍五入取整数
        "nutrients": "暂无"    # 营养素建议，目前暂未实现具体计算逻辑
    }

def calc_energy(age: int, gender: int, weight: float, height: float, pla: float):
    """ 计算一日膳食能量需要量 """
    eer = 0
    if age < 3:
        if gender == 1:
            eer = (0.118 * weight + 3.59 * height - 1.55) * 238.85 * pla * 1.01
        else:
            eer = (0.127 * weight + 2.94 * height - 1.2) * 238.85 * pla * 1.01
    elif 3 <= age < 10:
        if gender == 1:
            eer = (0.0632 * weight + 1.31 * height + 1.28) * 238.85 * pla * 1.01
        else:
            eer = (0.0666 * weight + 0.878 * height + 1.46) * 238.85 * pla * 1.01
    elif 10 <= age < 18:
        if gender == 1:
            eer = (0.0651 * weight + 1.11 * height + 1.25) * 238.85 * pla * 1.01
        else:
            eer = (0.0393 * weight + 1.04 * height + 1.93) * 238.85 * pla * 1.01
    elif 18 <= age < 50:
        eer = (14.52 * weight - 155.88 * gender + 565.79) * pla
    elif 50 <= age < 65:
        eer = (14.52 * weight - 155.88 * gender + 565.79) * pla * 0.95
    elif 65 <= age < 75:
        eer = (14.52 * weight - 155.88 * gender + 565.79) * pla * 0.925
    else:
        eer = (14.52 * weight - 155.88 * gender + 565.79) * pla * 0.9
    return round(eer)

def calc_nutrients():
    """ 一日三餐营养素建议 """

def calc_nutrients_carb(eer: int,age: int):
    """ 碳水化合物 """
    if age < 1:
        return 80
    elif 1 <= age < 65:
        return err * 0.5 / 4

def calc_nutrients_fat():
    pass

def calc_nutrients_protein():
    pass

def calc_nutrients_fiber():
    pass

if __name__ == "__main__":
    print(calc(0, 32, 1.6, 49.5))